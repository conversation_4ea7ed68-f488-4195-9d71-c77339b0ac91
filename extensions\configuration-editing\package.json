{"name": "configuration-editing", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.0.0"}, "icon": "images/icon.png", "activationEvents": ["onProfile", "onProfile:github", "onLanguage:json", "onLanguage:jsonc"], "enabledApiProposals": ["profileContentHandlers"], "main": "./out/configurationEditingMain", "browser": "./dist/browser/configurationEditingMain", "scripts": {"compile": "gulp compile-extension:configuration-editing", "watch": "gulp watch-extension:configuration-editing"}, "dependencies": {"@octokit/rest": "^21.1.1", "jsonc-parser": "^3.2.0", "tunnel": "^0.0.6"}, "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "contributes": {"languages": [{"id": "jsonc", "extensions": [".code-workspace", "language-configuration.json", "icon-theme.json", "color-theme.json"], "filenames": ["settings.json", "launch.json", "tasks.json", "mcp.json", "keybindings.json", "extensions.json", "argv.json", "profiles.json", "devcontainer.json", ".devcontainer.json"]}, {"id": "json", "extensions": [".code-profile"]}], "jsonValidation": [{"fileMatch": "vscode://defaultsettings/keybindings.json", "url": "vscode://schemas/keybindings"}, {"fileMatch": "%APP_SETTINGS_HOME%/keybindings.json", "url": "vscode://schemas/keybindings"}, {"fileMatch": "%APP_SETTINGS_HOME%/profiles/*/keybindings.json", "url": "vscode://schemas/keybindings"}, {"fileMatch": "vscode://defaultsettings/*.json", "url": "vscode://schemas/settings/default"}, {"fileMatch": "%APP_SETTINGS_HOME%/settings.json", "url": "vscode://schemas/settings/user"}, {"fileMatch": "%APP_SETTINGS_HOME%/profiles/*/settings.json", "url": "vscode://schemas/settings/profile"}, {"fileMatch": "%MACHINE_SETTINGS_HOME%/settings.json", "url": "vscode://schemas/settings/machine"}, {"fileMatch": "%APP_WORKSPACES_HOME%/*/workspace.json", "url": "vscode://schemas/workspaceConfig"}, {"fileMatch": "**/*.code-workspace", "url": "vscode://schemas/workspaceConfig"}, {"fileMatch": "**/argv.json", "url": "vscode://schemas/argv"}, {"fileMatch": "/.vscode/settings.json", "url": "vscode://schemas/settings/folder"}, {"fileMatch": "/.vscode/launch.json", "url": "vscode://schemas/launch"}, {"fileMatch": "/.vscode/tasks.json", "url": "vscode://schemas/tasks"}, {"fileMatch": "/.vscode/mcp.json", "url": "vscode://schemas/mcp"}, {"fileMatch": "%APP_SETTINGS_HOME%/tasks.json", "url": "vscode://schemas/tasks"}, {"fileMatch": "%APP_SETTINGS_HOME%/snippets/*.json", "url": "vscode://schemas/snippets"}, {"fileMatch": "%APP_SETTINGS_HOME%/prompts/*.toolsets.jsonc", "url": "vscode://schemas/toolsets"}, {"fileMatch": "%APP_SETTINGS_HOME%/profiles/*/snippets/.json", "url": "vscode://schemas/snippets"}, {"fileMatch": "%APP_SETTINGS_HOME%/sync/snippets/preview/*.json", "url": "vscode://schemas/snippets"}, {"fileMatch": "**/*.code-snippets", "url": "vscode://schemas/global-snippets"}, {"fileMatch": "/.vscode/extensions.json", "url": "vscode://schemas/extensions"}, {"fileMatch": "devcontainer.json", "url": "https://raw.githubusercontent.com/devcontainers/spec/main/schemas/devContainer.schema.json"}, {"fileMatch": ".devcontainer.json", "url": "https://raw.githubusercontent.com/devcontainers/spec/main/schemas/devContainer.schema.json"}, {"fileMatch": "%APP_SETTINGS_HOME%/globalStorage/ms-vscode-remote.remote-containers/nameConfigs/*.json", "url": "./schemas/attachContainer.schema.json"}, {"fileMatch": "%APP_SETTINGS_HOME%/globalStorage/ms-vscode-remote.remote-containers/imageConfigs/*.json", "url": "./schemas/attachContainer.schema.json"}, {"fileMatch": "**/quality/*/product.json", "url": "vscode://schemas/vscode-product"}]}, "devDependencies": {"@types/node": "22.x"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}
{"description": "Extension to add Jake capabilities to VS Code.", "displayName": "Jake support for VS Code", "jake.taskDefinition.type.description": "The <PERSON> task to customize.", "jake.taskDefinition.file.description": "The Jake file that provides the task. Can be omitted.", "config.jake.autoDetect": "Controls enablement of <PERSON> task detection. <PERSON> task detection can cause files in any open workspace to be executed."}
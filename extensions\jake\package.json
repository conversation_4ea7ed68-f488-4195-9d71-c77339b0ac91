{"name": "jake", "publisher": "vscode", "description": "%description%", "displayName": "%displayName%", "icon": "images/cowboy_hat.png", "version": "1.0.0", "license": "MIT", "engines": {"vscode": "*"}, "categories": ["Other"], "scripts": {"compile": "gulp compile-extension:jake", "watch": "gulp watch-extension:jake"}, "dependencies": {}, "devDependencies": {"@types/node": "22.x"}, "main": "./out/main", "activationEvents": ["onTaskType:jake"], "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": true}}, "contributes": {"configuration": {"id": "jake", "type": "object", "title": "<PERSON>", "properties": {"jake.autoDetect": {"scope": "application", "type": "string", "enum": ["off", "on"], "default": "off", "description": "%config.jake.autoDetect%"}}}, "taskDefinitions": [{"type": "jake", "required": ["task"], "properties": {"task": {"type": "string", "description": "%jake.taskDefinition.type.description%"}, "file": {"type": "string", "description": "%jake.taskDefinition.file.description%"}}, "when": "shellExecutionSupported"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}
{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"customizations": {"type": "object", "properties": {"codespaces": {"type": "object", "description": "Customizations specific to GitHub Codespaces", "properties": {"repositories": {"type": "object", "description": "Configuration relative to the given repositories, following the format 'owner/repo'.\n  A wildcard (*) is permitted for the repo name (eg: 'microsoft/*')", "patternProperties": {"^[a-zA-Z0-9-_.]+[.]*/[a-zA-Z0-9-_*]+[.]*$": {"type": "object", "additionalProperties": true, "oneOf": [{"properties": {"permissions": {"type": "object", "description": "Additional repository permissions.\n See https://aka.ms/ghcs/multi-repo-auth for more info.", "additionalProperties": true, "anyOf": [{"properties": {"actions": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"checks": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"contents": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"deployments": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"discussions": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"issues": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"packages": {"type": "string", "enum": ["read"]}}}, {"properties": {"pages": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"pull_requests": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"repository_projects": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"statuses": {"type": "string", "enum": ["read", "write"]}}}, {"properties": {"workflows": {"type": "string", "enum": ["write"]}}}]}}}, {"properties": {"permissions": {"type": "string", "description": "Additional repository permissions.\n See https://aka.ms/ghcs/multi-repo-auth for more info.", "enum": ["read-all", "write-all"]}}}]}}}, "openFiles": {"type": "array", "description": "The paths to the files to open when the codespace is created. Paths are relative to the workspace.", "items": {"type": "string"}}, "disableAutomaticConfiguration": {"type": "boolean", "description": "Disables the setup that is automatically run in a codespace if no `postCreateCommand` is specified.", "default": false}}}}}, "codespaces": {"type": "object", "additionalProperties": true, "description": "Codespaces-specific configuration.", "deprecated": true, "deprecationMessage": "Use 'customizations/codespaces' instead"}}}
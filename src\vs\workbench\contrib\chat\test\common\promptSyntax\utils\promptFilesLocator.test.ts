/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import assert from 'assert';
import { CancellationToken } from '../../../../../../../base/common/cancellation.js';
import { match } from '../../../../../../../base/common/glob.js';
import { Schemas } from '../../../../../../../base/common/network.js';
import { basename, relativePath } from '../../../../../../../base/common/resources.js';
import { URI } from '../../../../../../../base/common/uri.js';
import { mock } from '../../../../../../../base/test/common/mock.js';
import { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../../../../base/test/common/utils.js';
import { IConfigurationOverrides, IConfigurationService } from '../../../../../../../platform/configuration/common/configuration.js';
import { IFileService } from '../../../../../../../platform/files/common/files.js';
import { FileService } from '../../../../../../../platform/files/common/fileService.js';
import { InMemoryFileSystemProvider } from '../../../../../../../platform/files/common/inMemoryFilesystemProvider.js';
import { TestInstantiationService } from '../../../../../../../platform/instantiation/test/common/instantiationServiceMock.js';
import { ILogService, NullLogService } from '../../../../../../../platform/log/common/log.js';
import { IWorkspace, IWorkspaceContextService, IWorkspaceFolder } from '../../../../../../../platform/workspace/common/workspace.js';
import { IWorkbenchEnvironmentService } from '../../../../../../services/environment/common/environmentService.js';
import { IFileMatch, IFileQuery, ISearchService } from '../../../../../../services/search/common/search.js';
import { IUserDataProfileService } from '../../../../../../services/userDataProfile/common/userDataProfile.js';
import { PromptsConfig } from '../../../../common/promptSyntax/config/config.js';
import { PromptsType } from '../../../../common/promptSyntax/promptTypes.js';
import { isValidGlob, PromptFilesLocator } from '../../../../common/promptSyntax/utils/promptFilesLocator.js';
import { IMockFolder, MockFilesystem } from '../testUtils/mockFilesystem.js';
import { mockService } from './mock.js';

/**
 * Mocked instance of {@link IConfigurationService}.
 */
function mockConfigService<T>(value: T): IConfigurationService {
	return mockService<IConfigurationService>({
		getValue(key?: string | IConfigurationOverrides) {
			assert(
				typeof key === 'string',
				`Expected string configuration key, got '${typeof key}'.`,
			);
			if ('explorer.excludeGitIgnore' === key) {
				return false;
			}

			assert(
				[PromptsConfig.KEY, PromptsConfig.PROMPT_LOCATIONS_KEY, PromptsConfig.INSTRUCTIONS_LOCATION_KEY, PromptsConfig.MODE_LOCATION_KEY].includes(key),
				`Unsupported configuration key '${key}'.`,
			);

			return value;
		},
	});
}

/**
 * Mocked instance of {@link IWorkspaceContextService}.
 */
function mockWorkspaceService(folders: IWorkspaceFolder[]): IWorkspaceContextService {
	return mockService<IWorkspaceContextService>({
		getWorkspace(): IWorkspace {
			return new class extends mock<IWorkspace>() {
				override folders = folders;
			};
		},
		getWorkspaceFolder(): IWorkspaceFolder | null {
			return null;
		}

	});
}

suite('PromptFilesLocator', () => {
	const disposables = ensureNoDisposablesAreLeakedInTestSuite();

	// if (isWindows) {
	// 	return;
	// }

	let instantiationService: TestInstantiationService;
	setup(async () => {
		instantiationService = disposables.add(new TestInstantiationService());
		instantiationService.stub(ILogService, new NullLogService());

		const fileService = disposables.add(instantiationService.createInstance(FileService));
		const fileSystemProvider = disposables.add(new InMemoryFileSystemProvider());
		disposables.add(fileService.registerProvider(Schemas.file, fileSystemProvider));

		instantiationService.stub(IFileService, fileService);
	});

	/**
	 * Create a new instance of {@link PromptFilesLocator} with provided mocked
	 * values for configuration and workspace services.
	 */
	const createPromptsLocator = async (
		configValue: unknown,
		workspaceFolderPaths: string[],
		filesystem: IMockFolder[],
	): Promise<PromptFilesLocator> => {

		await (instantiationService.createInstance(MockFilesystem, filesystem)).mock();

		instantiationService.stub(IConfigurationService, mockConfigService(configValue));

		const workspaceFolders = workspaceFolderPaths.map((path, index) => {
			const uri = URI.file(path);

			return new class extends mock<IWorkspaceFolder>() {
				override uri = uri;
				override name = basename(uri);
				override index = index;
			};
		});
		instantiationService.stub(IWorkspaceContextService, mockWorkspaceService(workspaceFolders));
		instantiationService.stub(IWorkbenchEnvironmentService, {} as IWorkbenchEnvironmentService);
		instantiationService.stub(IUserDataProfileService, {} as IUserDataProfileService);
		instantiationService.stub(ISearchService, {
			async fileSearch(query: IFileQuery) {
				// mock the search service
				const fs = instantiationService.get(IFileService);
				const findFilesInLocation = async (location: URI, results: URI[] = []) => {
					try {
						const resolve = await fs.resolve(location);
						if (resolve.isFile) {
							results.push(resolve.resource);
						} else if (resolve.isDirectory && resolve.children) {
							for (const child of resolve.children) {
								await findFilesInLocation(child.resource, results);
							}
						}
					} catch (error) {
					}
					return results;
				};
				const results: IFileMatch[] = [];
				for (const folderQuery of query.folderQueries) {
					const allFiles = await findFilesInLocation(folderQuery.folder);
					for (const resource of allFiles) {
						const pathInFolder = relativePath(folderQuery.folder, resource) ?? '';
						if (query.filePattern === undefined || match(query.filePattern, pathInFolder)) {
							results.push({ resource });
						}
					}

				}
				return { results, messages: [] };
			}
		});
		return instantiationService.createInstance(PromptFilesLocator);
	};

	suite('empty workspace', () => {
		const EMPTY_WORKSPACE: string[] = [];

		suite('empty filesystem', () => {
			test('no config value', async () => {
				const locator = await createPromptsLocator(undefined, EMPTY_WORKSPACE, []);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[],
					'No prompts must be found.',
				);
				locator.dispose();
			});

			test('object config value', async () => {
				const locator = await createPromptsLocator({
					'/Users/<USER>/repos/prompts/': true,
					'/tmp/prompts/': false,
				}, EMPTY_WORKSPACE, []);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[],
					'No prompts must be found.',
				);
				locator.dispose();
			});

			test('array config value', async () => {
				const locator = await createPromptsLocator([
					'relative/path/to/prompts/',
					'/abs/path',
				], EMPTY_WORKSPACE, []);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[],
					'No prompts must be found.',
				);
				locator.dispose();
			});

			test('null config value', async () => {
				const locator = await createPromptsLocator(null, EMPTY_WORKSPACE, []);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[],
					'No prompts must be found.',
				);
				locator.dispose();
			});

			test('string config value', async () => {
				const locator = await createPromptsLocator('/etc/hosts/prompts', EMPTY_WORKSPACE, []);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[],
					'No prompts must be found.',
				);
				locator.dispose();
			});
		});

		suite('non-empty filesystem', () => {
			test('core logic', async () => {
				const locator = await createPromptsLocator(
					{
						'/Users/<USER>/repos/prompts': true,
						'/tmp/prompts/': true,
						'/absolute/path/prompts': false,
						'.copilot/prompts': true,
					},
					EMPTY_WORKSPACE,
					[
						{
							name: '/Users/<USER>/repos/prompts',
							children: [
								{
									name: 'test.prompt.md',
									contents: 'Hello, World!',
								},
								{
									name: 'refactor-tests.prompt.md',
									contents: 'some file content goes here',
								},
							],
						},
						{
							name: '/tmp/prompts',
							children: [
								{
									name: 'translate.to-rust.prompt.md',
									contents: 'some more random file contents',
								},
							],
						},
						{
							name: '/absolute/path/prompts',
							children: [
								{
									name: 'some-prompt-file.prompt.md',
									contents: 'hey hey hey',
								},
							],
						},
					]);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[
						'/Users/<USER>/repos/prompts/test.prompt.md',
						'/Users/<USER>/repos/prompts/refactor-tests.prompt.md',
						'/tmp/prompts/translate.to-rust.prompt.md'
					],
					'Must find correct prompts.',
				);
				locator.dispose();
			});

			suite('absolute', () => {
				suite('wild card', () => {
					const settings = [
						'/Users/<USER>/repos/vscode/**',
						'/Users/<USER>/repos/vscode/**/*.prompt.md',
						'/Users/<USER>/repos/vscode/**/*.md',
						'/Users/<USER>/repos/vscode/**/*',
						'/Users/<USER>/repos/vscode/deps/**',
						'/Users/<USER>/repos/vscode/deps/**/*.prompt.md',
						'/Users/<USER>/repos/vscode/deps/**/*',
						'/Users/<USER>/repos/vscode/deps/**/*.md',
						'/Users/<USER>/repos/vscode/**/text/**',
						'/Users/<USER>/repos/vscode/**/text/**/*',
						'/Users/<USER>/repos/vscode/**/text/**/*.md',
						'/Users/<USER>/repos/vscode/**/text/**/*.prompt.md',
						'/Users/<USER>/repos/vscode/deps/text/**',
						'/Users/<USER>/repos/vscode/deps/text/**/*',
						'/Users/<USER>/repos/vscode/deps/text/**/*.md',
						'/Users/<USER>/repos/vscode/deps/text/**/*.prompt.md',
					];

					for (const setting of settings) {
						test(`• '${setting}'`, async () => {
							const locator = await createPromptsLocator(
								{ [setting]: true },
								EMPTY_WORKSPACE,
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'deps/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rabot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/deps/text/my.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific2.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});

				suite(`• specific`, () => {
					const testSettings = [
						[
							'/Users/<USER>/repos/vscode/**/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/**/*specific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/*specific*.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/specific*',
							'/Users/<USER>/repos/vscode/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/vscode/**/unspecific2.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/**/unspecific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/nested/specific.prompt.md',
							'/Users/<USER>/repos/vscode/**/nested/unspecific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/nested/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/**/*spec*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/*spec*',
						],
						[
							'/Users/<USER>/repos/vscode/**/*spec*.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/deps/**/*spec*.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/text/**/*spec*.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/nested/*spec*',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/nested/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/specific*',
							'/Users/<USER>/repos/vscode/deps/**/unspecific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/specific*.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific*.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific2.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific1*.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific2*.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/specific*',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/specific*.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific*.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific2.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific1*.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific2*.md',
						],
					];

					for (const settings of testSettings) {
						test(`• '${JSON.stringify(settings)}'`, async () => {
							const vscodeSettings: Record<string, boolean> = {};
							for (const setting of settings) {
								vscodeSettings[setting] = true;
							}

							const locator = await createPromptsLocator(
								vscodeSettings,
								EMPTY_WORKSPACE,
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'deps/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'default.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rawbot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/deps/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific2.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});
			});
		});
	});

	suite('single-root workspace', () => {
		suite('glob pattern', () => {
			suite('relative', () => {
				suite('wild card', () => {
					const testSettings = [
						'**',
						'**/*.prompt.md',
						'**/*.md',
						'**/*',
						'deps/**',
						'deps/**/*.prompt.md',
						'deps/**/*',
						'deps/**/*.md',
						'**/text/**',
						'**/text/**/*',
						'**/text/**/*.md',
						'**/text/**/*.prompt.md',
						'deps/text/**',
						'deps/text/**/*',
						'deps/text/**/*.md',
						'deps/text/**/*.prompt.md',
					];

					for (const setting of testSettings) {
						test(`• '${setting}'`, async () => {
							const locator = await createPromptsLocator(
								{ [setting]: true },
								['/Users/<USER>/repos/vscode'],
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'deps/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rabot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/deps/text/my.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific2.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});

				suite(`• specific`, () => {
					const testSettings = [
						[
							'**/*specific*',
						],
						[
							'**/*specific*.prompt.md',
						],
						[
							'**/*specific*.md',
						],
						[
							'**/specific*',
							'**/unspecific1.prompt.md',
							'**/unspecific2.prompt.md',
						],
						[
							'**/specific.prompt.md',
							'**/unspecific*.prompt.md',
						],
						[
							'**/nested/specific.prompt.md',
							'**/nested/unspecific*.prompt.md',
						],
						[
							'**/nested/*specific*',
						],
						[
							'**/*spec*.prompt.md',
						],
						[
							'**/*spec*',
						],
						[
							'**/*spec*.md',
						],
						[
							'**/deps/**/*spec*.md',
						],
						[
							'**/text/**/*spec*.md',
						],
						[
							'deps/text/nested/*spec*',
						],
						[
							'deps/text/nested/*specific*',
						],
						[
							'deps/**/*specific*',
						],
						[
							'deps/**/specific*',
							'deps/**/unspecific*.prompt.md',
						],
						[
							'deps/**/specific*.md',
							'deps/**/unspecific*.md',
						],
						[
							'deps/**/specific.prompt.md',
							'deps/**/unspecific1.prompt.md',
							'deps/**/unspecific2.prompt.md',
						],
						[
							'deps/**/specific.prompt.md',
							'deps/**/unspecific1*.md',
							'deps/**/unspecific2*.md',
						],
						[
							'deps/text/**/*specific*',
						],
						[
							'deps/text/**/specific*',
							'deps/text/**/unspecific*.prompt.md',
						],
						[
							'deps/text/**/specific*.md',
							'deps/text/**/unspecific*.md',
						],
						[
							'deps/text/**/specific.prompt.md',
							'deps/text/**/unspecific1.prompt.md',
							'deps/text/**/unspecific2.prompt.md',
						],
						[
							'deps/text/**/specific.prompt.md',
							'deps/text/**/unspecific1*.md',
							'deps/text/**/unspecific2*.md',
						],
					];

					for (const settings of testSettings) {
						test(`• '${JSON.stringify(settings)}'`, async () => {
							const vscodeSettings: Record<string, boolean> = {};
							for (const setting of settings) {
								vscodeSettings[setting] = true;
							}

							const locator = await createPromptsLocator(
								vscodeSettings,
								['/Users/<USER>/repos/vscode'],
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'deps/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'default.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rawbot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/deps/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific2.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});
			});

			suite('absolute', () => {
				suite('wild card', () => {
					const settings = [
						'/Users/<USER>/repos/vscode/**',
						'/Users/<USER>/repos/vscode/**/*.prompt.md',
						'/Users/<USER>/repos/vscode/**/*.md',
						'/Users/<USER>/repos/vscode/**/*',
						'/Users/<USER>/repos/vscode/deps/**',
						'/Users/<USER>/repos/vscode/deps/**/*.prompt.md',
						'/Users/<USER>/repos/vscode/deps/**/*',
						'/Users/<USER>/repos/vscode/deps/**/*.md',
						'/Users/<USER>/repos/vscode/**/text/**',
						'/Users/<USER>/repos/vscode/**/text/**/*',
						'/Users/<USER>/repos/vscode/**/text/**/*.md',
						'/Users/<USER>/repos/vscode/**/text/**/*.prompt.md',
						'/Users/<USER>/repos/vscode/deps/text/**',
						'/Users/<USER>/repos/vscode/deps/text/**/*',
						'/Users/<USER>/repos/vscode/deps/text/**/*.md',
						'/Users/<USER>/repos/vscode/deps/text/**/*.prompt.md',
					];

					for (const setting of settings) {
						test(`• '${setting}'`, async () => {
							const locator = await createPromptsLocator(
								{ [setting]: true },
								['/Users/<USER>/repos/vscode'],
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'deps/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rabot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/deps/text/my.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific2.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});

				suite(`• specific`, () => {
					const testSettings = [
						[
							'/Users/<USER>/repos/vscode/**/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/**/*specific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/*specific*.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/specific*',
							'/Users/<USER>/repos/vscode/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/vscode/**/unspecific2.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/**/unspecific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/nested/specific.prompt.md',
							'/Users/<USER>/repos/vscode/**/nested/unspecific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/nested/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/**/*spec*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/*spec*',
						],
						[
							'/Users/<USER>/repos/vscode/**/*spec*.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/deps/**/*spec*.md',
						],
						[
							'/Users/<USER>/repos/vscode/**/text/**/*spec*.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/nested/*spec*',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/nested/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/specific*',
							'/Users/<USER>/repos/vscode/deps/**/unspecific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/specific*.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific*.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific2.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific1*.md',
							'/Users/<USER>/repos/vscode/deps/**/unspecific2*.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/*specific*',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/specific*',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific*.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/specific*.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific*.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific2.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/deps/text/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific1*.md',
							'/Users/<USER>/repos/vscode/deps/text/**/unspecific2*.md',
						],
					];

					for (const settings of testSettings) {
						test(`• '${JSON.stringify(settings)}'`, async () => {
							const vscodeSettings: Record<string, boolean> = {};
							for (const setting of settings) {
								vscodeSettings[setting] = true;
							}

							const locator = await createPromptsLocator(
								vscodeSettings,
								['/Users/<USER>/repos/vscode'],
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'deps/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'default.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rawbot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/deps/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/deps/text/nested/unspecific2.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});
			});
		});
	});

	test('core logic', async () => {
		const locator = await createPromptsLocator(
			{
				'/Users/<USER>/repos/prompts': true,
				'/tmp/prompts/': true,
				'/absolute/path/prompts': false,
				'.copilot/prompts': true,
			},
			[
				'/Users/<USER>/repos/vscode',
			],
			[
				{
					name: '/Users/<USER>/repos/prompts',
					children: [
						{
							name: 'test.prompt.md',
							contents: 'Hello, World!',
						},
						{
							name: 'refactor-tests.prompt.md',
							contents: 'some file content goes here',
						},
					],
				},
				{
					name: '/tmp/prompts',
					children: [
						{
							name: 'translate.to-rust.prompt.md',
							contents: 'some more random file contents',
						},
					],
				},
				{
					name: '/absolute/path/prompts',
					children: [
						{
							name: 'some-prompt-file.prompt.md',
							contents: 'hey hey hey',
						},
					],
				},
				{
					name: '/Users/<USER>/repos/vscode',
					children: [
						{
							name: '.copilot/prompts',
							children: [
								{
									name: 'default.prompt.md',
									contents: 'oh hi, robot!',
								},
							],
						},
						{
							name: '.github/prompts',
							children: [
								{
									name: 'my.prompt.md',
									contents: 'oh hi, bot!',
								},
							],
						},
					],
				},
			]);

		assertOutcome(
			await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
			[
				'/Users/<USER>/repos/vscode/.github/prompts/my.prompt.md',
				'/Users/<USER>/repos/prompts/test.prompt.md',
				'/Users/<USER>/repos/prompts/refactor-tests.prompt.md',
				'/tmp/prompts/translate.to-rust.prompt.md',
				'/Users/<USER>/repos/vscode/.copilot/prompts/default.prompt.md',
			],
			'Must find correct prompts.',
		);
		locator.dispose();
	});

	test('with disabled `.github/prompts` location', async () => {
		const locator = await createPromptsLocator(
			{
				'/Users/<USER>/repos/prompts': true,
				'/tmp/prompts/': true,
				'/absolute/path/prompts': false,
				'.copilot/prompts': true,
				'.github/prompts': false,
			},
			[
				'/Users/<USER>/repos/vscode',
			],
			[
				{
					name: '/Users/<USER>/repos/prompts',
					children: [
						{
							name: 'test.prompt.md',
							contents: 'Hello, World!',
						},
						{
							name: 'refactor-tests.prompt.md',
							contents: 'some file content goes here',
						},
					],
				},
				{
					name: '/tmp/prompts',
					children: [
						{
							name: 'translate.to-rust.prompt.md',
							contents: 'some more random file contents',
						},
					],
				},
				{
					name: '/absolute/path/prompts',
					children: [
						{
							name: 'some-prompt-file.prompt.md',
							contents: 'hey hey hey',
						},
					],
				},
				{
					name: '/Users/<USER>/repos/vscode',
					children: [
						{
							name: '.copilot/prompts',
							children: [
								{
									name: 'default.prompt.md',
									contents: 'oh hi, robot!',
								},
							],
						},
						{
							name: '.github/prompts',
							children: [
								{
									name: 'my.prompt.md',
									contents: 'oh hi, bot!',
								},
								{
									name: 'your.prompt.md',
									contents: 'oh hi, bot!',
								},
							],
						},
					],
				},
			]);

		assertOutcome(
			await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
			[
				'/Users/<USER>/repos/prompts/test.prompt.md',
				'/Users/<USER>/repos/prompts/refactor-tests.prompt.md',
				'/tmp/prompts/translate.to-rust.prompt.md',
				'/Users/<USER>/repos/vscode/.copilot/prompts/default.prompt.md',
			],
			'Must find correct prompts.',
		);
		locator.dispose();
	});

	suite('multi-root workspace', () => {
		suite('core logic', () => {
			test('without top-level `.github` folder', async () => {
				const locator = await createPromptsLocator(
					{
						'/Users/<USER>/repos/prompts': true,
						'/tmp/prompts/': true,
						'/absolute/path/prompts': false,
						'.copilot/prompts': false,
					},
					[
						'/Users/<USER>/repos/vscode',
						'/Users/<USER>/repos/node',
					],
					[
						{
							name: '/Users/<USER>/repos/prompts',
							children: [
								{
									name: 'test.prompt.md',
									contents: 'Hello, World!',
								},
								{
									name: 'refactor-tests.prompt.md',
									contents: 'some file content goes here',
								},
							],
						},
						{
							name: '/tmp/prompts',
							children: [
								{
									name: 'translate.to-rust.prompt.md',
									contents: 'some more random file contents',
								},
							],
						},
						{
							name: '/absolute/path/prompts',
							children: [
								{
									name: 'some-prompt-file.prompt.md',
									contents: 'hey hey hey',
								},
							],
						},
						{
							name: '/Users/<USER>/repos/vscode',
							children: [
								{
									name: '.copilot/prompts',
									children: [
										{
											name: 'prompt1.prompt.md',
											contents: 'oh hi, robot!',
										},
									],
								},
								{
									name: '.github/prompts',
									children: [
										{
											name: 'default.prompt.md',
											contents: 'oh hi, bot!',
										},
									],
								},
							],
						},
						{
							name: '/Users/<USER>/repos/node',
							children: [
								{
									name: '.copilot/prompts',
									children: [
										{
											name: 'prompt5.prompt.md',
											contents: 'oh hi, robot!',
										},
									],
								},
								{
									name: '.github/prompts',
									children: [
										{
											name: 'refactor-static-classes.prompt.md',
											contents: 'file contents',
										},
									],
								},
							],
						},
						// note! this folder is not part of the workspace, so prompt files are `ignored`
						{
							name: '/Users/<USER>/repos/.github/prompts',
							children: [
								{
									name: 'prompt-name.prompt.md',
									contents: 'oh hi, robot!',
								},
								{
									name: 'name-of-the-prompt.prompt.md',
									contents: 'oh hi, raw bot!',
								},
							],
						},
					]);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[
						'/Users/<USER>/repos/vscode/.github/prompts/default.prompt.md',
						'/Users/<USER>/repos/node/.github/prompts/refactor-static-classes.prompt.md',
						'/Users/<USER>/repos/prompts/test.prompt.md',
						'/Users/<USER>/repos/prompts/refactor-tests.prompt.md',
						'/tmp/prompts/translate.to-rust.prompt.md',
					],
					'Must find correct prompts.',
				);
				locator.dispose();
			});

			test('with top-level `.github` folder', async () => {
				const locator = await createPromptsLocator(
					{
						'/Users/<USER>/repos/prompts': true,
						'/tmp/prompts/': true,
						'/absolute/path/prompts': false,
						'.copilot/prompts': false,
					},
					[
						'/Users/<USER>/repos/vscode',
						'/Users/<USER>/repos/node',
						'/var/shared/prompts',
					],
					[
						{
							name: '/Users/<USER>/repos/prompts',
							children: [
								{
									name: 'test.prompt.md',
									contents: 'Hello, World!',
								},
								{
									name: 'refactor-tests.prompt.md',
									contents: 'some file content goes here',
								},
							],
						},
						{
							name: '/tmp/prompts',
							children: [
								{
									name: 'translate.to-rust.prompt.md',
									contents: 'some more random file contents',
								},
							],
						},
						{
							name: '/absolute/path/prompts',
							children: [
								{
									name: 'some-prompt-file.prompt.md',
									contents: 'hey hey hey',
								},
							],
						},
						{
							name: '/Users/<USER>/repos/vscode',
							children: [
								{
									name: '.copilot/prompts',
									children: [
										{
											name: 'prompt1.prompt.md',
											contents: 'oh hi, robot!',
										},
									],
								},
								{
									name: '.github/prompts',
									children: [
										{
											name: 'default.prompt.md',
											contents: 'oh hi, bot!',
										},
									],
								},
							],
						},
						{
							name: '/Users/<USER>/repos/node',
							children: [
								{
									name: '.copilot/prompts',
									children: [
										{
											name: 'prompt5.prompt.md',
											contents: 'oh hi, robot!',
										},
									],
								},
								{
									name: '.github/prompts',
									children: [
										{
											name: 'refactor-static-classes.prompt.md',
											contents: 'file contents',
										},
									],
								},
							],
						},
						// note! this folder is part of the workspace, so prompt files are `included`
						{
							name: '/var/shared/prompts/.github/prompts',
							children: [
								{
									name: 'prompt-name.prompt.md',
									contents: 'oh hi, robot!',
								},
								{
									name: 'name-of-the-prompt.prompt.md',
									contents: 'oh hi, raw bot!',
								},
							],
						},
					]);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[
						'/Users/<USER>/repos/vscode/.github/prompts/default.prompt.md',
						'/Users/<USER>/repos/node/.github/prompts/refactor-static-classes.prompt.md',
						'/var/shared/prompts/.github/prompts/prompt-name.prompt.md',
						'/var/shared/prompts/.github/prompts/name-of-the-prompt.prompt.md',
						'/Users/<USER>/repos/prompts/test.prompt.md',
						'/Users/<USER>/repos/prompts/refactor-tests.prompt.md',
						'/tmp/prompts/translate.to-rust.prompt.md',
					],
					'Must find correct prompts.',
				);
				locator.dispose();
			});

			test('with disabled `.github/prompts` location', async () => {
				const locator = await createPromptsLocator(
					{
						'/Users/<USER>/repos/prompts': true,
						'/tmp/prompts/': true,
						'/absolute/path/prompts': false,
						'.copilot/prompts': false,
						'.github/prompts': false,
					},
					[
						'/Users/<USER>/repos/vscode',
						'/Users/<USER>/repos/node',
						'/var/shared/prompts',
					],
					[
						{
							name: '/Users/<USER>/repos/prompts',
							children: [
								{
									name: 'test.prompt.md',
									contents: 'Hello, World!',
								},
								{
									name: 'refactor-tests.prompt.md',
									contents: 'some file content goes here',
								},
							],
						},
						{
							name: '/tmp/prompts',
							children: [
								{
									name: 'translate.to-rust.prompt.md',
									contents: 'some more random file contents',
								},
							],
						},
						{
							name: '/absolute/path/prompts',
							children: [
								{
									name: 'some-prompt-file.prompt.md',
									contents: 'hey hey hey',
								},
							],
						},
						{
							name: '/Users/<USER>/repos/vscode',
							children: [
								{
									name: '.copilot/prompts',
									children: [
										{
											name: 'prompt1.prompt.md',
											contents: 'oh hi, robot!',
										},
									],
								},
								{
									name: '.github/prompts',
									children: [
										{
											name: 'default.prompt.md',
											contents: 'oh hi, bot!',
										},
									],
								},
							],
						},
						{
							name: '/Users/<USER>/repos/node',
							children: [
								{
									name: '.copilot/prompts',
									children: [
										{
											name: 'prompt5.prompt.md',
											contents: 'oh hi, robot!',
										},
									],
								},
								{
									name: '.github/prompts',
									children: [
										{
											name: 'refactor-static-classes.prompt.md',
											contents: 'file contents',
										},
									],
								},
							],
						},
						// note! this folder is part of the workspace, so prompt files are `included`
						{
							name: '/var/shared/prompts/.github/prompts',
							children: [
								{
									name: 'prompt-name.prompt.md',
									contents: 'oh hi, robot!',
								},
								{
									name: 'name-of-the-prompt.prompt.md',
									contents: 'oh hi, raw bot!',
								},
							],
						},
					]);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[
						'/Users/<USER>/repos/prompts/test.prompt.md',
						'/Users/<USER>/repos/prompts/refactor-tests.prompt.md',
						'/tmp/prompts/translate.to-rust.prompt.md',
					],
					'Must find correct prompts.',
				);
				locator.dispose();
			});

			test('mixed', async () => {
				const locator = await createPromptsLocator(
					{
						'/Users/<USER>/repos/**/*test*': true,
						'.copilot/prompts': false,
						'.github/prompts': true,
						'/absolute/path/prompts/some-prompt-file.prompt.md': true,
					},
					[
						'/Users/<USER>/repos/vscode',
						'/Users/<USER>/repos/node',
						'/var/shared/prompts',
					],
					[
						{
							name: '/Users/<USER>/repos/prompts',
							children: [
								{
									name: 'test.prompt.md',
									contents: 'Hello, World!',
								},
								{
									name: 'refactor-tests.prompt.md',
									contents: 'some file content goes here',
								},
								{
									name: 'elf.prompt.md',
									contents: 'haalo!',
								},
							],
						},
						{
							name: '/tmp/prompts',
							children: [
								{
									name: 'translate.to-rust.prompt.md',
									contents: 'some more random file contents',
								},
							],
						},
						{
							name: '/absolute/path/prompts',
							children: [
								{
									name: 'some-prompt-file.prompt.md',
									contents: 'hey hey hey',
								},
							],
						},
						{
							name: '/Users/<USER>/repos/vscode',
							children: [
								{
									name: '.copilot/prompts',
									children: [
										{
											name: 'prompt1.prompt.md',
											contents: 'oh hi, robot!',
										},
									],
								},
								{
									name: '.github/prompts',
									children: [
										{
											name: 'default.prompt.md',
											contents: 'oh hi, bot!',
										},
									],
								},
							],
						},
						{
							name: '/Users/<USER>/repos/node',
							children: [
								{
									name: '.copilot/prompts',
									children: [
										{
											name: 'prompt5.prompt.md',
											contents: 'oh hi, robot!',
										},
									],
								},
								{
									name: '.github/prompts',
									children: [
										{
											name: 'refactor-static-classes.prompt.md',
											contents: 'file contents',
										},
									],
								},
							],
						},
						// note! this folder is part of the workspace, so prompt files are `included`
						{
							name: '/var/shared/prompts/.github/prompts',
							children: [
								{
									name: 'prompt-name.prompt.md',
									contents: 'oh hi, robot!',
								},
								{
									name: 'name-of-the-prompt.prompt.md',
									contents: 'oh hi, raw bot!',
								},
							],
						},
					]);

				assertOutcome(
					await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
					[
						// all of these are due to the `.github/prompts` setting
						'/Users/<USER>/repos/vscode/.github/prompts/default.prompt.md',
						'/Users/<USER>/repos/node/.github/prompts/refactor-static-classes.prompt.md',
						'/var/shared/prompts/.github/prompts/prompt-name.prompt.md',
						'/var/shared/prompts/.github/prompts/name-of-the-prompt.prompt.md',
						// all of these are due to the `/Users/<USER>/repos/**/*test*` setting
						'/Users/<USER>/repos/prompts/test.prompt.md',
						'/Users/<USER>/repos/prompts/refactor-tests.prompt.md',
						// this one is due to the specific `/absolute/path/prompts/some-prompt-file.prompt.md` setting
						'/absolute/path/prompts/some-prompt-file.prompt.md',
					],
					'Must find correct prompts.',
				);
				locator.dispose();
			});
		});

		suite('glob pattern', () => {
			suite('relative', () => {
				suite('wild card', () => {
					const testSettings = [
						'**',
						'**/*.prompt.md',
						'**/*.md',
						'**/*',
						'gen*/**',
						'gen*/**/*.prompt.md',
						'gen*/**/*',
						'gen*/**/*.md',
						'**/gen*/**',
						'**/gen*/**/*',
						'**/gen*/**/*.md',
						'**/gen*/**/*.prompt.md',
						'{generic,general,gen}/**',
						'{generic,general,gen}/**/*.prompt.md',
						'{generic,general,gen}/**/*',
						'{generic,general,gen}/**/*.md',
						'**/{generic,general,gen}/**',
						'**/{generic,general,gen}/**/*',
						'**/{generic,general,gen}/**/*.md',
						'**/{generic,general,gen}/**/*.prompt.md',
					];

					for (const setting of testSettings) {
						test(`• '${setting}'`, async () => {
							const locator = await createPromptsLocator(
								{ [setting]: true },
								[
									'/Users/<USER>/repos/vscode',
									'/Users/<USER>/repos/prompts',
								],
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'gen/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rabot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
									{
										name: '/Users/<USER>/repos/prompts',
										children: [
											{
												name: 'general',
												children: [
													{
														name: 'common.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'uncommon-10.prompt.md',
														contents: 'oh hi, robot!',
													},
													{
														name: 'license.md',
														contents: 'non prompt file',
													},
												],
											}
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/gen/text/my.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/unspecific2.prompt.md',
									// -
									'/Users/<USER>/repos/prompts/general/common.prompt.md',
									'/Users/<USER>/repos/prompts/general/uncommon-10.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});

				suite(`• specific`, () => {
					const testSettings = [
						[
							'**/my.prompt.md',
							'**/*specific*',
							'**/*common*',
						],
						[
							'**/my.prompt.md',
							'**/*specific*.prompt.md',
							'**/*common*.prompt.md',
						],
						[
							'**/my*.md',
							'**/*specific*.md',
							'**/*common*.md',
						],
						[
							'**/my*.md',
							'**/specific*',
							'**/unspecific*',
							'**/common*',
							'**/uncommon*',
						],
						[
							'**/my.prompt.md',
							'**/specific.prompt.md',
							'**/unspecific1.prompt.md',
							'**/unspecific2.prompt.md',
							'**/common.prompt.md',
							'**/uncommon-10.prompt.md',
						],
						[
							'gen*/**/my.prompt.md',
							'gen*/**/*specific*',
							'gen*/**/*common*',
						],
						[
							'gen*/**/my.prompt.md',
							'gen*/**/*specific*.prompt.md',
							'gen*/**/*common*.prompt.md',
						],
						[
							'gen*/**/my*.md',
							'gen*/**/*specific*.md',
							'gen*/**/*common*.md',
						],
						[
							'gen*/**/my*.md',
							'gen*/**/specific*',
							'gen*/**/unspecific*',
							'gen*/**/common*',
							'gen*/**/uncommon*',
						],
						[
							'gen*/**/my.prompt.md',
							'gen*/**/specific.prompt.md',
							'gen*/**/unspecific1.prompt.md',
							'gen*/**/unspecific2.prompt.md',
							'gen*/**/common.prompt.md',
							'gen*/**/uncommon-10.prompt.md',
						],
						[
							'gen/text/my.prompt.md',
							'gen/text/nested/specific.prompt.md',
							'gen/text/nested/unspecific1.prompt.md',
							'gen/text/nested/unspecific2.prompt.md',
							'general/common.prompt.md',
							'general/uncommon-10.prompt.md',
						],
						[
							'gen/text/my.prompt.md',
							'gen/text/nested/*specific*',
							'general/*common*',
						],
						[
							'gen/text/my.prompt.md',
							'gen/text/**/specific.prompt.md',
							'gen/text/**/unspecific1.prompt.md',
							'gen/text/**/unspecific2.prompt.md',
							'general/*',
						],
						[
							'{gen,general}/**/my.prompt.md',
							'{gen,general}/**/*specific*',
							'{gen,general}/**/*common*',
						],
						[
							'{gen,general}/**/my.prompt.md',
							'{gen,general}/**/*specific*.prompt.md',
							'{gen,general}/**/*common*.prompt.md',
						],
						[
							'{gen,general}/**/my*.md',
							'{gen,general}/**/*specific*.md',
							'{gen,general}/**/*common*.md',
						],
						[
							'{gen,general}/**/my*.md',
							'{gen,general}/**/specific*',
							'{gen,general}/**/unspecific*',
							'{gen,general}/**/common*',
							'{gen,general}/**/uncommon*',
						],
						[
							'{gen,general}/**/my.prompt.md',
							'{gen,general}/**/specific.prompt.md',
							'{gen,general}/**/unspecific1.prompt.md',
							'{gen,general}/**/unspecific2.prompt.md',
							'{gen,general}/**/common.prompt.md',
							'{gen,general}/**/uncommon-10.prompt.md',
						],
					];

					for (const settings of testSettings) {
						test(`• '${JSON.stringify(settings)}'`, async () => {
							const vscodeSettings: Record<string, boolean> = {};
							for (const setting of settings) {
								vscodeSettings[setting] = true;
							}

							const locator = await createPromptsLocator(
								vscodeSettings,
								[
									'/Users/<USER>/repos/vscode',
									'/Users/<USER>/repos/prompts',
								],
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'gen/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rabot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
									{
										name: '/Users/<USER>/repos/prompts',
										children: [
											{
												name: 'general',
												children: [
													{
														name: 'common.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'uncommon-10.prompt.md',
														contents: 'oh hi, robot!',
													},
													{
														name: 'license.md',
														contents: 'non prompt file',
													},
												],
											}
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/gen/text/my.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/unspecific2.prompt.md',
									// -
									'/Users/<USER>/repos/prompts/general/common.prompt.md',
									'/Users/<USER>/repos/prompts/general/uncommon-10.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});
			});

			suite('absolute', () => {
				suite('wild card', () => {
					const testSettings = [
						'/Users/<USER>/repos/**',
						'/Users/<USER>/repos/**/*.prompt.md',
						'/Users/<USER>/repos/**/*.md',
						'/Users/<USER>/repos/**/*',
						'/Users/<USER>/repos/**/gen*/**',
						'/Users/<USER>/repos/**/gen*/**/*.prompt.md',
						'/Users/<USER>/repos/**/gen*/**/*',
						'/Users/<USER>/repos/**/gen*/**/*.md',
						'/Users/<USER>/repos/**/gen*/**',
						'/Users/<USER>/repos/**/gen*/**/*',
						'/Users/<USER>/repos/**/gen*/**/*.md',
						'/Users/<USER>/repos/**/gen*/**/*.prompt.md',
						'/Users/<USER>/repos/{vscode,prompts}/**',
						'/Users/<USER>/repos/{vscode,prompts}/**/*.prompt.md',
						'/Users/<USER>/repos/{vscode,prompts}/**/*.md',
						'/Users/<USER>/repos/{vscode,prompts}/**/*',
						'/Users/<USER>/repos/{vscode,prompts}/**/gen*/**',
						'/Users/<USER>/repos/{vscode,prompts}/**/gen*/**/*.prompt.md',
						'/Users/<USER>/repos/{vscode,prompts}/**/gen*/**/*',
						'/Users/<USER>/repos/{vscode,prompts}/**/gen*/**/*.md',
						'/Users/<USER>/repos/{vscode,prompts}/**/gen*/**',
						'/Users/<USER>/repos/{vscode,prompts}/**/gen*/**/*',
						'/Users/<USER>/repos/{vscode,prompts}/**/gen*/**/*.md',
						'/Users/<USER>/repos/{vscode,prompts}/**/gen*/**/*.prompt.md',
						'/Users/<USER>/repos/{vscode,prompts}/**/{general,gen}/**',
						'/Users/<USER>/repos/{vscode,prompts}/**/{general,gen}/**/*.prompt.md',
						'/Users/<USER>/repos/{vscode,prompts}/**/{general,gen}/**/*',
						'/Users/<USER>/repos/{vscode,prompts}/**/{general,gen}/**/*.md',
						'/Users/<USER>/repos/{vscode,prompts}/**/{general,gen}/**',
						'/Users/<USER>/repos/{vscode,prompts}/**/{general,gen}/**/*',
						'/Users/<USER>/repos/{vscode,prompts}/**/{general,gen}/**/*.md',
						'/Users/<USER>/repos/{vscode,prompts}/**/{general,gen}/**/*.prompt.md',
					];

					for (const setting of testSettings) {
						test(`• '${setting}'`, async () => {
							const locator = await createPromptsLocator(
								{ [setting]: true },
								[
									'/Users/<USER>/repos/vscode',
									'/Users/<USER>/repos/prompts',
								],
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'gen/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rabot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
									{
										name: '/Users/<USER>/repos/prompts',
										children: [
											{
												name: 'general',
												children: [
													{
														name: 'common.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'uncommon-10.prompt.md',
														contents: 'oh hi, robot!',
													},
													{
														name: 'license.md',
														contents: 'non prompt file',
													},
												],
											}
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/gen/text/my.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/unspecific2.prompt.md',
									// -
									'/Users/<USER>/repos/prompts/general/common.prompt.md',
									'/Users/<USER>/repos/prompts/general/uncommon-10.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});

				suite(`• specific`, () => {
					const testSettings = [
						[
							'/Users/<USER>/repos/**/my.prompt.md',
							'/Users/<USER>/repos/**/*specific*',
							'/Users/<USER>/repos/**/*common*',
						],
						[
							'/Users/<USER>/repos/**/my.prompt.md',
							'/Users/<USER>/repos/**/*specific*.prompt.md',
							'/Users/<USER>/repos/**/*common*.prompt.md',
						],
						[
							'/Users/<USER>/repos/**/my*.md',
							'/Users/<USER>/repos/**/*specific*.md',
							'/Users/<USER>/repos/**/*common*.md',
						],
						[
							'/Users/<USER>/repos/**/my*.md',
							'/Users/<USER>/repos/**/specific*',
							'/Users/<USER>/repos/**/unspecific*',
							'/Users/<USER>/repos/**/common*',
							'/Users/<USER>/repos/**/uncommon*',
						],
						[
							'/Users/<USER>/repos/**/my.prompt.md',
							'/Users/<USER>/repos/**/specific.prompt.md',
							'/Users/<USER>/repos/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/**/unspecific2.prompt.md',
							'/Users/<USER>/repos/**/common.prompt.md',
							'/Users/<USER>/repos/**/uncommon-10.prompt.md',
						],
						[
							'/Users/<USER>/repos/**/gen*/**/my.prompt.md',
							'/Users/<USER>/repos/**/gen*/**/*specific*',
							'/Users/<USER>/repos/**/gen*/**/*common*',
						],
						[
							'/Users/<USER>/repos/**/gen*/**/my.prompt.md',
							'/Users/<USER>/repos/**/gen*/**/*specific*.prompt.md',
							'/Users/<USER>/repos/**/gen*/**/*common*.prompt.md',
						],
						[
							'/Users/<USER>/repos/**/gen*/**/my*.md',
							'/Users/<USER>/repos/**/gen*/**/*specific*.md',
							'/Users/<USER>/repos/**/gen*/**/*common*.md',
						],
						[
							'/Users/<USER>/repos/**/gen*/**/my*.md',
							'/Users/<USER>/repos/**/gen*/**/specific*',
							'/Users/<USER>/repos/**/gen*/**/unspecific*',
							'/Users/<USER>/repos/**/gen*/**/common*',
							'/Users/<USER>/repos/**/gen*/**/uncommon*',
						],
						[
							'/Users/<USER>/repos/**/gen*/**/my.prompt.md',
							'/Users/<USER>/repos/**/gen*/**/specific.prompt.md',
							'/Users/<USER>/repos/**/gen*/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/**/gen*/**/unspecific2.prompt.md',
							'/Users/<USER>/repos/**/gen*/**/common.prompt.md',
							'/Users/<USER>/repos/**/gen*/**/uncommon-10.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/gen/text/my.prompt.md',
							'/Users/<USER>/repos/vscode/gen/text/nested/specific.prompt.md',
							'/Users/<USER>/repos/vscode/gen/text/nested/unspecific1.prompt.md',
							'/Users/<USER>/repos/vscode/gen/text/nested/unspecific2.prompt.md',
							'/Users/<USER>/repos/prompts/general/common.prompt.md',
							'/Users/<USER>/repos/prompts/general/uncommon-10.prompt.md',
						],
						[
							'/Users/<USER>/repos/vscode/gen/text/my.prompt.md',
							'/Users/<USER>/repos/vscode/gen/text/nested/*specific*',
							'/Users/<USER>/repos/prompts/general/*common*',
						],
						[
							'/Users/<USER>/repos/vscode/gen/text/my.prompt.md',
							'/Users/<USER>/repos/vscode/gen/text/**/specific.prompt.md',
							'/Users/<USER>/repos/vscode/gen/text/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/vscode/gen/text/**/unspecific2.prompt.md',
							'/Users/<USER>/repos/prompts/general/*',
						],
						[
							'/Users/<USER>/repos/**/{gen,general}/**/my.prompt.md',
							'/Users/<USER>/repos/**/{gen,general}/**/*specific*',
							'/Users/<USER>/repos/**/{gen,general}/**/*common*',
						],
						[
							'/Users/<USER>/repos/**/{gen,general}/**/my.prompt.md',
							'/Users/<USER>/repos/**/{gen,general}/**/*specific*.prompt.md',
							'/Users/<USER>/repos/**/{gen,general}/**/*common*.prompt.md',
						],
						[
							'/Users/<USER>/repos/**/{gen,general}/**/my*.md',
							'/Users/<USER>/repos/**/{gen,general}/**/*specific*.md',
							'/Users/<USER>/repos/**/{gen,general}/**/*common*.md',
						],
						[
							'/Users/<USER>/repos/**/{gen,general}/**/my*.md',
							'/Users/<USER>/repos/**/{gen,general}/**/specific*',
							'/Users/<USER>/repos/**/{gen,general}/**/unspecific*',
							'/Users/<USER>/repos/**/{gen,general}/**/common*',
							'/Users/<USER>/repos/**/{gen,general}/**/uncommon*',
						],
						[
							'/Users/<USER>/repos/**/{gen,general}/**/my.prompt.md',
							'/Users/<USER>/repos/**/{gen,general}/**/specific.prompt.md',
							'/Users/<USER>/repos/**/{gen,general}/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/**/{gen,general}/**/unspecific2.prompt.md',
							'/Users/<USER>/repos/**/{gen,general}/**/common.prompt.md',
							'/Users/<USER>/repos/**/{gen,general}/**/uncommon-10.prompt.md',
						],
						[
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/my.prompt.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/*specific*',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/*common*',
						],
						[
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/my.prompt.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/*specific*.prompt.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/*common*.prompt.md',
						],
						[
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/my*.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/*specific*.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/*common*.md',
						],
						[
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/my*.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/specific*',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/unspecific*',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/common*',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/uncommon*',
						],
						[
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/my.prompt.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/specific.prompt.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/unspecific1.prompt.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/unspecific2.prompt.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/common.prompt.md',
							'/Users/<USER>/repos/{prompts,vscode,copilot}/{gen,general}/**/uncommon-10.prompt.md',
						],
					];

					for (const settings of testSettings) {
						test(`• '${JSON.stringify(settings)}'`, async () => {
							const vscodeSettings: Record<string, boolean> = {};
							for (const setting of settings) {
								vscodeSettings[setting] = true;
							}

							const locator = await createPromptsLocator(
								vscodeSettings,
								[
									'/Users/<USER>/repos/vscode',
									'/Users/<USER>/repos/prompts',
								],
								[
									{
										name: '/Users/<USER>/repos/vscode',
										children: [
											{
												name: 'gen/text',
												children: [
													{
														name: 'my.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'nested',
														children: [
															{
																name: 'specific.prompt.md',
																contents: 'oh hi, bot!',
															},
															{
																name: 'unspecific1.prompt.md',
																contents: 'oh hi, robot!',
															},
															{
																name: 'unspecific2.prompt.md',
																contents: 'oh hi, rabot!',
															},
															{
																name: 'readme.md',
																contents: 'non prompt file',
															},
														],
													}
												],
											},
										],
									},
									{
										name: '/Users/<USER>/repos/prompts',
										children: [
											{
												name: 'general',
												children: [
													{
														name: 'common.prompt.md',
														contents: 'oh hi, bot!',
													},
													{
														name: 'uncommon-10.prompt.md',
														contents: 'oh hi, robot!',
													},
													{
														name: 'license.md',
														contents: 'non prompt file',
													},
												],
											}
										],
									},
								],
							);

							assertOutcome(
								await locator.listFiles(PromptsType.prompt, 'local', CancellationToken.None),
								[
									'/Users/<USER>/repos/vscode/gen/text/my.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/specific.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/unspecific1.prompt.md',
									'/Users/<USER>/repos/vscode/gen/text/nested/unspecific2.prompt.md',
									// -
									'/Users/<USER>/repos/prompts/general/common.prompt.md',
									'/Users/<USER>/repos/prompts/general/uncommon-10.prompt.md',
								],
								'Must find correct prompts.',
							);
							locator.dispose();
						});
					}
				});
			});
		});
	});

	suite('isValidGlob', () => {
		test('valid patterns', () => {
			const globs = [
				'**',
				'\*',
				'\**',
				'**/*',
				'**/*.prompt.md',
				'/Users/<USER>/**/*.prompt.md',
				'/Users/<USER>/*.prompt.md',
				'/Users/<USER>/*',
				'/Users/<USER>/repos/{repo1,test}',
				'/Users/<USER>/repos/{repo1,test}/**',
				'/Users/<USER>/repos/{repo1,test}/*',
				'/Users/<USER>/**/{repo1,test}/**',
				'/Users/<USER>/**/{repo1,test}',
				'/Users/<USER>/**/{repo1,test}/*',
				'/Users/<USER>/**/repo[1,2,3]',
				'/Users/<USER>/**/repo[1,2,3]/**',
				'/Users/<USER>/**/repo[1,2,3]/*',
				'/Users/<USER>/**/repo[1,2,3]/**/*.prompt.md',
				'repo[1,2,3]/**/*.prompt.md',
				'repo[[1,2,3]/**/*.prompt.md',
				'{repo1,test}/*.prompt.md',
				'{repo1,test}/*',
				'/{repo1,test}/*',
				'/{repo1,test}}/*',
			];

			for (const glob of globs) {
				assert(
					(isValidGlob(glob) === true),
					`'${glob}' must be a 'valid' glob pattern.`,
				);
			}
		});

		test('invalid patterns', () => {
			const globs = [
				'.',
				'\\*',
				'\\?',
				'\\*\\?\\*',
				'repo[1,2,3',
				'repo1,2,3]',
				'repo\\[1,2,3]',
				'repo[1,2,3\\]',
				'repo\\[1,2,3\\]',
				'{repo1,repo2',
				'repo1,repo2}',
				'\\{repo1,repo2}',
				'{repo1,repo2\\}',
				'\\{repo1,repo2\\}',
				'/Users/<USER>/repos',
				'/Users/<USER>/repo[1,2,3',
				'/Users/<USER>/repo1,2,3]',
				'/Users/<USER>/repo\\[1,2,3]',
				'/Users/<USER>/repo[1,2,3\\]',
				'/Users/<USER>/repo\\[1,2,3\\]',
				'/Users/<USER>/{repo1,repo2',
				'/Users/<USER>/repo1,repo2}',
				'/Users/<USER>/\\{repo1,repo2}',
				'/Users/<USER>/{repo1,repo2\\}',
				'/Users/<USER>/\\{repo1,repo2\\}',
			];

			for (const glob of globs) {
				assert(
					(isValidGlob(glob) === false),
					`'${glob}' must be an 'invalid' glob pattern.`,
				);
			}
		});
	});

	suite('getConfigBasedSourceFolders', () => {
		test('gets unambiguous list of folders', async () => {
			const locator = await createPromptsLocator(
				{
					'.github/prompts': true,
					'/Users/<USER>/repos/**': true,
					'gen/text/**': true,
					'gen/text/nested/*.prompt.md': true,
					'general/*': true,
					'/Users/<USER>/repos/vscode/my-prompts': true,
					'/Users/<USER>/repos/vscode/your-prompts/*.md': true,
					'/Users/<USER>/repos/prompts/shared-prompts/*': true,
				},
				[
					'/Users/<USER>/repos/vscode',
					'/Users/<USER>/repos/prompts',
				],
				[],
			);

			assertOutcome(
				locator.getConfigBasedSourceFolders(PromptsType.prompt),
				[
					'/Users/<USER>/repos/vscode/.github/prompts',
					'/Users/<USER>/repos/prompts/.github/prompts',
					'/Users/<USER>/repos/vscode/gen/text/nested',
					'/Users/<USER>/repos/prompts/gen/text/nested',
					'/Users/<USER>/repos/vscode/general',
					'/Users/<USER>/repos/prompts/general',
					'/Users/<USER>/repos/vscode/my-prompts',
					'/Users/<USER>/repos/vscode/your-prompts',
					'/Users/<USER>/repos/prompts/shared-prompts',
				],
				'Must find correct prompts.',
			);
			locator.dispose();
		});
	});
});

function assertOutcome(actual: readonly URI[], expected: string[], message: string) {
	assert.deepStrictEqual(actual.map((uri) => uri.path), expected, message);
}

{"registrations": [{"component": {"type": "git", "git": {"name": "microsoft/vscode-css", "repositoryUrl": "https://github.com/microsoft/vscode-css", "commitHash": "a927fe2f73927bf5c25d0b0c4dd0e63d69fd8887"}}, "licenseDetail": ["MIT License", "", "Copyright (c) Microsoft Corporation.", "", "Permission is hereby granted, free of charge, to any person obtaining", "a copy of this software and associated documentation files (the", "\"Software\"), to deal in the Software without restriction, including", "without limitation the rights to use, copy, modify, merge, publish,", "distribute, sublicense, and/or sell copies of the Software, and to", "permit persons to whom the Software is furnished to do so, subject to", "the following conditions:", "", "The above copyright notice and this permission notice shall be", "included in all copies or substantial portions of the Software.", "", "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,", "EXPRESS OR <PERSON><PERSON><PERSON><PERSON><PERSON>, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF", "MERCHANTABILITY, FITNE<PERSON> FOR A PARTICULAR PURPOSE AND", "NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE", "LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION", "OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION", "WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.", "", "--------------------------------------------------------------------", "", "This package was derived from a TextMate bundle located at", "https://github.com/textmate/css.tmbundle and distributed under the following", "license, located in `README.mdown`:", "", "Permission to copy, use, modify, sell and distribute this", "software is granted. This software is provided \"as is\" without", "express or implied warranty, and with no claim as to its", "suitability for any purpose."], "license": "MIT License", "description": "The file syntaxes/css.tmLanguage.json was derived from https://github.com/atom/language-css which was originally converted from the TextMate bundle https://github.com/textmate/css.tmbundle.", "version": "0.0.0"}], "version": 1}
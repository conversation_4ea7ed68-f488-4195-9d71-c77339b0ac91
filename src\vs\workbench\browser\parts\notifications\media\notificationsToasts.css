/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench > .notifications-toasts {
	position: absolute;
	z-index: 1000;
	right: 3px;
	bottom: 25px; /* 22px status bar height + 3px */
	display: none;
	overflow: hidden;
}

.monaco-workbench.nostatusbar > .notifications-toasts {
	bottom: 3px;
}

.monaco-workbench > .notifications-toasts.visible {
	display: flex;
	flex-direction: column;
}

.monaco-workbench > .notifications-toasts .notification-toast-container {
	overflow: hidden; /* this ensures that the notification toast does not shine through */
}

.monaco-workbench > .notifications-toasts .notification-toast-container > .notification-toast {
	margin: 4px; /* enables separation and drop shadows around toasts */
	transform: translate3d(0px, 100%, 0px); /* move the notification 50px to the bottom (to prevent bleed through) */
	opacity: 0; /*  fade the toast in */
	transition:	transform 300ms ease-out, opacity 300ms ease-out;
}

.monaco-workbench > .notifications-toasts .notifications-list-container,
.monaco-workbench > .notifications-toasts .notification-toast-container > .notification-toast,
.monaco-workbench > .notifications-toasts .notification-toast-container > .notification-toast .monaco-scrollable-element,
.monaco-workbench > .notifications-toasts .notification-toast-container > .notification-toast .monaco-list:not(.element-focused):focus:before,
.monaco-workbench > .notifications-toasts .notification-toast-container > .notification-toast .monaco-list-row {
	border-radius: 4px;
}

.monaco-workbench.reduce-motion > .notifications-toasts .notification-toast-container > .notification-toast {
	transition:	transform 0ms ease-out, opacity 0ms ease-out;
}

.monaco-workbench > .notifications-toasts .notification-toast-container > .notification-toast.notification-fade-in {
	opacity: 1;
	transform: none;
}

.monaco-workbench > .notifications-toasts .notification-toast-container > .notification-toast.notification-fade-in-done {
	opacity: 1;
	transform: none;
	transition:	none;
}

/* Icons */

.monaco-workbench > .notifications-toasts .codicon.codicon-error {
	color: var(--vscode-notificationsErrorIcon-foreground) !important;
}

.monaco-workbench > .notifications-toasts .codicon.codicon-warning {
	color: var(--vscode-notificationsWarningIcon-foreground) !important;
}

.monaco-workbench > .notifications-toasts .codicon.codicon-info {
	color: var(--vscode-notificationsInfoIcon-foreground) !important;
}

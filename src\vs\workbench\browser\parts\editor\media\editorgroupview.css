/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/* Container */

.monaco-workbench .part.editor > .content .editor-group-container {
	height: 100%;
}

.monaco-workbench .part.editor > .content .editor-group-container.empty  {
	opacity: 0.5; /* dimmed to indicate inactive state */
}

.monaco-workbench .part.editor > .content .editor-group-container.empty.active,
.monaco-workbench .part.editor > .content .editor-group-container.empty.dragged-over {
	opacity: 1; /* indicate active/dragged-over group through undimmed state */
}

.monaco-workbench .part.editor > .content:not(.empty) .editor-group-container.empty.active:focus {
	outline-offset: -2px;
	outline: 1px solid var(--vscode-editorGroup-focusedEmptyBorder);
}

.monaco-workbench .part.editor > .content.empty .editor-group-container.empty.active:focus {
	outline: none; /* never show outline for empty group if it is the last */
}

/* Watermark & shortcuts */

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-watermark  {
	display: flex;
	height: 100%;
	max-width: 290px;
	margin: auto;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.monaco-workbench .part.editor > .content .editor-group-container:not(.empty) > .editor-group-watermark {
	display: none;
}

.monaco-workbench .part.editor > .content:not(.empty) .editor-group-container.empty > .editor-group-watermark,
.monaco-workbench .part.editor > .content.auxiliary .editor-group-container.empty > .editor-group-watermark {
	max-width: 200px;
	height: calc(100% - 70px);
}

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-watermark > .letterpress {
	width: 100%;
	max-height: 100%;
	aspect-ratio: 1/1;
	background-image: url('./letterpress-light.svg');
	background-size: contain;
	background-position-x: center;
	background-repeat: no-repeat;
}

.monaco-workbench.vs-dark .part.editor > .content .editor-group-container .editor-group-watermark > .letterpress {
	background-image: url('./letterpress-dark.svg');
}

.monaco-workbench.hc-light .part.editor > .content .editor-group-container .editor-group-watermark > .letterpress {
	background-image: url('./letterpress-hcLight.svg');
}

.monaco-workbench.hc-black .part.editor > .content .editor-group-container .editor-group-watermark > .letterpress {
	background-image: url('./letterpress-hcDark.svg');
}

.monaco-workbench .part.editor > .content:not(.empty) .editor-group-container > .editor-group-watermark > .shortcuts,
.monaco-workbench .part.editor > .content.auxiliary .editor-group-container > .editor-group-watermark > .shortcuts,
.monaco-workbench .part.editor > .content .editor-group-container.max-height-478px > .editor-group-watermark > .shortcuts {
	display: none;
}

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-watermark > .shortcuts > .watermark-box {
	display: inline-table;
	border-collapse: separate;
	border-spacing: 11px 17px;
}

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-watermark > .shortcuts dl {
	display: table-row;
	opacity: .8;
	cursor: default;
	color: var(--vscode-editorWatermark-foreground);
}

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-watermark > .shortcuts dt {
	text-align: right;
	letter-spacing: 0.04em
}

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-watermark > .shortcuts dd {
	text-align: left;
}

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-watermark > .shortcuts dt,
.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-watermark > .shortcuts dd {
	display: table-cell;
	vertical-align: middle;
}

/* Title */

.monaco-workbench .part.editor > .content .editor-group-container > .title {
	position: relative;
	box-sizing:	border-box;
	overflow: hidden;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title:not(.tabs) {
	display: flex; /* when tabs are not shown, use flex layout */
	flex-wrap: nowrap;
}

.monaco-workbench .part.editor > .content .editor-group-container > .title.title-border-bottom::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 9;
	pointer-events: none;
	background-color: var(--title-border-bottom-color);
	width: 100%;
	height: 1px;
}

.monaco-workbench .part.editor > .content .editor-group-container.empty > .title {
	display: none;
}

/* Toolbar */

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-container-toolbar {
	display: none;
	height: 35px;
}

.monaco-workbench .part.editor > .content .editor-group-container.empty.locked > .editor-group-container-toolbar,
.monaco-workbench .part.editor > .content:not(.empty) .editor-group-container.empty > .editor-group-container-toolbar,
.monaco-workbench .part.editor > .content.auxiliary .editor-group-container.empty > .editor-group-container-toolbar {
	display: block; /* show toolbar when more than one editor group or always when auxiliary or locked */
}

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-container-toolbar .actions-container {
	justify-content: flex-end;
}

.monaco-workbench .part.editor > .content .editor-group-container > .editor-group-container-toolbar .action-item {
	margin-right: 4px;
}

/* Editor */

.monaco-workbench .part.editor > .content .editor-group-container.empty > .editor-container  {
	display: none;
}

.monaco-workbench .part.editor > .content .editor-group-container > .editor-container > .editor-instance {
	height: 100%;
}

.monaco-workbench .part.editor > .content .grid-view-container {
	width: 100%;
	height: 100%;
}
